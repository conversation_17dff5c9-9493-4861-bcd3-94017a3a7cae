#!/bin/bash

echo "🔧 Claude API 520错误修复脚本"
echo "================================"
echo ""

# 重新加载配置
source ~/.zshrc

echo "📋 当前配置："
echo "  API Key: ${ANTHROPIC_API_KEY:0:20}..."
echo "  Base URL: $ANTHROPIC_BASE_URL"
echo "  Auth Token: ${ANTHROPIC_AUTH_TOKEN:0:20}..."
echo ""

echo "🔍 诊断520错误原因..."
echo ""

# 测试1: 检查网络连接
echo "1️⃣ 测试网络连接..."
if ping -c 1 pmpjfbhq.cn-nb1.rainapp.top >/dev/null 2>&1; then
    echo "   ✅ 网络连接正常"
else
    echo "   ❌ 网络连接失败"
fi

# 测试2: 检查HTTPS连接
echo ""
echo "2️⃣ 测试HTTPS连接..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -I https://pmpjfbhq.cn-nb1.rainapp.top --connect-timeout 5 --max-time 10)
if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    echo "   ✅ HTTPS连接正常 (状态码: $HTTP_STATUS)"
else
    echo "   ❌ HTTPS连接异常 (状态码: $HTTP_STATUS)"
fi

# 测试3: 测试API端点
echo ""
echo "3️⃣ 测试API端点..."
API_RESPONSE=$(timeout 15 curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "https://pmpjfbhq.cn-nb1.rainapp.top/v1/messages" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $ANTHROPIC_API_KEY" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "max_tokens": 10,
    "messages": [{"role": "user", "content": "Hi"}]
  }' 2>/dev/null)

if [ $? -eq 124 ]; then
    echo "   ⏰ 请求超时"
    echo ""
    echo "🔄 尝试原始端点..."
    
    # 尝试原始端点
    export ANTHROPIC_BASE_URL="https://anyrouter.top"
    API_RESPONSE=$(timeout 15 curl -s -w "\nHTTP_STATUS:%{http_code}" \
      -X POST "https://anyrouter.top/v1/messages" \
      -H "Content-Type: application/json" \
      -H "x-api-key: $ANTHROPIC_API_KEY" \
      -H "anthropic-version: 2023-06-01" \
      -d '{
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 10,
        "messages": [{"role": "user", "content": "Hi"}]
      }' 2>/dev/null)
    
    if [ $? -eq 124 ]; then
        echo "   ⏰ 原始端点也超时"
    else
        HTTP_STATUS=$(echo "$API_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
        echo "   📊 原始端点状态码: $HTTP_STATUS"
        if [ "$HTTP_STATUS" = "200" ]; then
            echo "   ✅ 原始端点工作正常！"
            echo ""
            echo "💡 建议：更新配置使用原始端点"
            sed -i '' 's|https://pmpjfbhq.cn-nb1.rainapp.top|https://anyrouter.top|g' ~/.zshrc
            echo "   配置已更新为: https://anyrouter.top"
        fi
    fi
else
    HTTP_STATUS=$(echo "$API_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    echo "   📊 API状态码: $HTTP_STATUS"
    
    case $HTTP_STATUS in
        200)
            echo "   ✅ API调用成功！"
            ;;
        520)
            echo "   ❌ 520错误 - 服务器错误"
            echo "   💡 尝试其他解决方案..."
            ;;
        401)
            echo "   ❌ 401错误 - API密钥无效"
            ;;
        429)
            echo "   ❌ 429错误 - 请求过于频繁"
            ;;
        *)
            echo "   ❌ 其他错误"
            ;;
    esac
fi

echo ""
echo "🏁 诊断完成"
echo ""
echo "📝 如果仍有问题，请尝试："
echo "1. 检查API密钥是否正确"
echo "2. 确认账户余额充足"
echo "3. 联系API提供商确认服务状态"
echo "4. 尝试使用官方Anthropic API端点"
