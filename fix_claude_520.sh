#!/bin/bash

echo "🔧 Claude CLI 520错误修复脚本"
echo "============================"
echo ""

echo "📋 问题分析："
echo "1. ⚠️  认证冲突：Claude CLI检测到环境变量和控制台密钥冲突"
echo "2. 🌐 网络超时：API请求可能因代理或网络问题超时"
echo "3. 🔗 端点问题：当前API端点可能不稳定"
echo ""

echo "🔧 修复方案："
echo ""

# 方案1: 清理认证冲突
echo "1️⃣ 清理认证冲突..."
echo "   运行 claude /logout 清理现有认证"
echo "   (需要手动在Claude CLI中执行)"
echo ""

# 方案2: 测试不同的API端点
echo "2️⃣ 测试不同的API端点..."

# 备份当前配置
cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)

endpoints=(
    "https://anyrouter.top"
    "https://api.anthropic.com"
)

for endpoint in "${endpoints[@]}"; do
    echo ""
    echo "   测试端点: $endpoint"
    
    # 临时设置端点
    export ANTHROPIC_BASE_URL="$endpoint"
    
    # 测试API调用
    echo "   发送测试请求..."
    
    if [ "$endpoint" = "https://api.anthropic.com" ]; then
        echo "   ⚠️  官方端点需要官方API密钥，跳过测试"
        continue
    fi
    
    # 使用curl测试
    response=$(curl -s -w "\nSTATUS:%{http_code}" \
        -X POST "$endpoint/v1/messages" \
        -H "Content-Type: application/json" \
        -H "x-api-key: $ANTHROPIC_API_KEY" \
        -H "anthropic-version: 2023-06-01" \
        -d '{
            "model": "claude-3-sonnet-20240229",
            "max_tokens": 5,
            "messages": [{"role": "user", "content": "Hi"}]
        }' \
        --connect-timeout 10 \
        --max-time 15 2>/dev/null)
    
    status=$(echo "$response" | grep "STATUS:" | cut -d: -f2)
    
    echo "   📊 状态码: $status"
    
    case $status in
        200)
            echo "   ✅ 端点工作正常！"
            echo "   💡 建议使用此端点: $endpoint"
            
            # 更新配置文件
            sed -i '' "s|export ANTHROPIC_BASE_URL=.*|export ANTHROPIC_BASE_URL=\"$endpoint\"|g" ~/.zshrc
            echo "   ✅ 配置已更新到 ~/.zshrc"
            break
            ;;
        520)
            echo "   ❌ 520错误 - 服务器错误"
            ;;
        502)
            echo "   ❌ 502错误 - 网关错误"
            ;;
        "")
            echo "   ⏰ 请求超时或无响应"
            ;;
        *)
            echo "   ⚠️  其他错误: $status"
            ;;
    esac
done

echo ""
echo "3️⃣ 代理配置优化..."

# 测试禁用代理的效果
echo "   测试禁用代理的API调用..."
unset https_proxy http_proxy HTTPS_PROXY HTTP_PROXY

response_no_proxy=$(curl -s -w "\nSTATUS:%{http_code}" \
    -X POST "$ANTHROPIC_BASE_URL/v1/messages" \
    -H "Content-Type: application/json" \
    -H "x-api-key: $ANTHROPIC_API_KEY" \
    -H "anthropic-version: 2023-06-01" \
    -d '{
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 5,
        "messages": [{"role": "user", "content": "Hi"}]
    }' \
    --connect-timeout 10 \
    --max-time 15 2>/dev/null)

status_no_proxy=$(echo "$response_no_proxy" | grep "STATUS:" | cut -d: -f2)
echo "   📊 无代理状态码: $status_no_proxy"

# 重新加载代理
source ~/.zshrc

if [ "$status_no_proxy" = "200" ] && [ "$status" != "200" ]; then
    echo "   💡 建议：代理可能导致520错误，考虑为Claude CLI禁用代理"
    echo ""
    echo "   可以创建一个无代理的Claude启动脚本："
    cat > claude_no_proxy.sh << 'EOF'
#!/bin/bash
unset https_proxy http_proxy HTTPS_PROXY HTTP_PROXY
claude "$@"
EOF
    chmod +x claude_no_proxy.sh
    echo "   ✅ 已创建 claude_no_proxy.sh 脚本"
fi

echo ""
echo "4️⃣ 环境变量优化..."

# 检查是否需要使用ANTHROPIC_AUTH_TOKEN
echo "   测试使用ANTHROPIC_AUTH_TOKEN..."
unset ANTHROPIC_API_KEY
export ANTHROPIC_AUTH_TOKEN="sk-d3eVI4PiSAKMigh2REv5xO45oFXOhTMyqcnVYcxWhBTp49Om"

echo "   ✅ 已设置ANTHROPIC_AUTH_TOKEN，取消ANTHROPIC_API_KEY"

echo ""
echo "🏁 修复完成"
echo ""
echo "📝 下一步操作："
echo "1. 重新启动终端或运行: source ~/.zshrc"
echo "2. 运行: claude /logout (清理认证冲突)"
echo "3. 测试Claude CLI是否正常工作"
echo "4. 如果仍有问题，使用: ./claude_no_proxy.sh"
echo ""
echo "🔍 如果问题持续，可能需要："
echo "- 联系API提供商确认服务状态"
echo "- 检查API密钥是否有效"
echo "- 确认账户余额充足"
