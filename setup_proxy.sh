#!/bin/bash

# 检查.zshrc文件是否存在
ZSHRC_FILE="$HOME/.zshrc"

if [ ! -f "$ZSHRC_FILE" ]; then
    echo "创建新的.zshrc文件"
    touch "$ZSHRC_FILE"
fi

# 检查代理设置是否已存在
if grep -q "export https_proxy=http://127.0.0.1:10808" "$ZSHRC_FILE"; then
    echo "代理设置已存在于.zshrc文件中"
else
    # 添加代理设置到.zshrc文件
    echo "\n# 设置HTTPS代理" >> "$ZSHRC_FILE"
    echo "export https_proxy=http://127.0.0.1:10808" >> "$ZSHRC_FILE"
    echo "代理设置已成功添加到.zshrc文件中"
fi

echo "您可以通过运行以下命令立即应用设置："
echo "source ~/.zshrc"