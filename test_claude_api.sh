#!/bin/bash

# Claude API 测试脚本
echo "🧪 测试 Claude API 配置..."
echo ""

# 显示当前配置
echo "当前配置："
echo "  ANTHROPIC_AUTH_TOKEN: ${ANTHROPIC_AUTH_TOKEN:0:20}..."
echo "  ANTHROPIC_BASE_URL: $ANTHROPIC_BASE_URL"
echo ""

# 测试1: 使用当前配置
echo "📡 测试1: 使用当前配置..."
claude --print "Hello, this is a test message." 2>&1
test1_result=$?
echo ""

if [ $test1_result -eq 0 ]; then
    echo "✅ 测试1成功！API配置正常工作。"
else
    echo "❌ 测试1失败。尝试其他配置..."
    echo ""
    
    # 测试2: 使用官方API
    echo "📡 测试2: 使用官方 Anthropic API..."
    export ANTHROPIC_BASE_URL="https://api.anthropic.com"
    claude --print "Hello, this is a test with official API." 2>&1
    test2_result=$?
    echo ""
    
    if [ $test2_result -eq 0 ]; then
        echo "✅ 测试2成功！官方API工作正常。"
        echo "💡 建议：更新配置使用官方API端点。"
    else
        echo "❌ 测试2也失败。"
        echo ""
        echo "🔍 可能的问题："
        echo "1. API密钥无效或过期"
        echo "2. 账户余额不足"
        echo "3. 网络连接问题"
        echo ""
        echo "📝 建议："
        echo "1. 检查您的 Anthropic 账户余额"
        echo "2. 验证API密钥是否正确"
        echo "3. 联系API提供商确认服务状态"
    fi
    
    # 恢复原始配置
    export ANTHROPIC_BASE_URL="https://anyrouter.top"
fi

echo ""
echo "🏁 测试完成。"
