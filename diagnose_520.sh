#!/bin/bash

echo "🔍 Claude CLI 520错误详细诊断"
echo "============================="
echo ""

# 重新加载配置
source ~/.zshrc

echo "📋 当前配置："
echo "  API Key: ${ANTHROPIC_API_KEY:0:20}..."
echo "  Base URL: $ANTHROPIC_BASE_URL"
echo "  代理: $https_proxy"
echo ""

echo "🔍 诊断步骤："
echo ""

# 步骤1: 测试不同的API端点
echo "1️⃣ 测试不同API端点的连通性："
echo ""

endpoints=(
    "https://anyrouter.top"
    "https://pmpjfbhq.cn-nb1.rainapp.top"
    "https://api.anthropic.com"
)

for endpoint in "${endpoints[@]}"; do
    echo "   测试: $endpoint"
    
    # 测试基本连接
    status=$(curl -s -o /dev/null -w "%{http_code}" -I "$endpoint" --connect-timeout 5 --max-time 10 2>/dev/null)
    echo "     基本连接状态码: $status"
    
    # 测试API端点
    if [ "$endpoint" != "https://api.anthropic.com" ]; then
        api_status=$(timeout 10 curl -s -w "%{http_code}" \
            -X POST "$endpoint/v1/messages" \
            -H "Content-Type: application/json" \
            -H "x-api-key: $ANTHROPIC_API_KEY" \
            -H "anthropic-version: 2023-06-01" \
            -d '{"model": "claude-3-sonnet-20240229", "max_tokens": 5, "messages": [{"role": "user", "content": "Hi"}]}' \
            -o /dev/null 2>/dev/null)
        echo "     API调用状态码: $api_status"
    else
        echo "     (跳过官方API测试，需要官方密钥)"
    fi
    echo ""
done

# 步骤2: 测试不同的认证方式
echo "2️⃣ 测试不同的认证头："
echo ""

auth_headers=(
    "x-api-key"
    "Authorization: Bearer"
)

for auth in "${auth_headers[@]}"; do
    echo "   测试认证方式: $auth"
    
    if [ "$auth" = "x-api-key" ]; then
        api_response=$(timeout 10 curl -s -w "\nSTATUS:%{http_code}" \
            -X POST "$ANTHROPIC_BASE_URL/v1/messages" \
            -H "Content-Type: application/json" \
            -H "x-api-key: $ANTHROPIC_API_KEY" \
            -H "anthropic-version: 2023-06-01" \
            -d '{"model": "claude-3-sonnet-20240229", "max_tokens": 5, "messages": [{"role": "user", "content": "Hi"}]}' 2>/dev/null)
    else
        api_response=$(timeout 10 curl -s -w "\nSTATUS:%{http_code}" \
            -X POST "$ANTHROPIC_BASE_URL/v1/messages" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $ANTHROPIC_API_KEY" \
            -H "anthropic-version: 2023-06-01" \
            -d '{"model": "claude-3-sonnet-20240229", "max_tokens": 5, "messages": [{"role": "user", "content": "Hi"}]}' 2>/dev/null)
    fi
    
    status=$(echo "$api_response" | grep "STATUS:" | cut -d: -f2)
    response_body=$(echo "$api_response" | grep -v "STATUS:")
    
    echo "     状态码: $status"
    if [ -n "$response_body" ] && [ "$response_body" != "" ]; then
        echo "     响应: $(echo "$response_body" | head -c 100)..."
    fi
    echo ""
done

# 步骤3: 测试代理影响
echo "3️⃣ 测试代理对API调用的影响："
echo ""

echo "   a) 使用代理的API调用："
with_proxy=$(timeout 10 curl -s -w "%{http_code}" \
    --proxy "$https_proxy" \
    -X POST "$ANTHROPIC_BASE_URL/v1/messages" \
    -H "Content-Type: application/json" \
    -H "x-api-key: $ANTHROPIC_API_KEY" \
    -H "anthropic-version: 2023-06-01" \
    -d '{"model": "claude-3-sonnet-20240229", "max_tokens": 5, "messages": [{"role": "user", "content": "Hi"}]}' \
    -o /dev/null 2>/dev/null)
echo "     使用代理状态码: $with_proxy"

echo ""
echo "   b) 不使用代理的API调用："
without_proxy=$(timeout 10 curl -s -w "%{http_code}" \
    --noproxy "*" \
    -X POST "$ANTHROPIC_BASE_URL/v1/messages" \
    -H "Content-Type: application/json" \
    -H "x-api-key: $ANTHROPIC_API_KEY" \
    -H "anthropic-version: 2023-06-01" \
    -d '{"model": "claude-3-sonnet-20240229", "max_tokens": 5, "messages": [{"role": "user", "content": "Hi"}]}' \
    -o /dev/null 2>/dev/null)
echo "     不使用代理状态码: $without_proxy"

# 步骤4: 检查Claude CLI的具体错误
echo ""
echo "4️⃣ 测试Claude CLI具体错误："
echo ""

echo "   运行Claude CLI并捕获错误..."
claude_output=$(echo "Hello" | timeout 15 claude --print 2>&1)
claude_exit_code=$?

echo "   Claude CLI退出码: $claude_exit_code"
echo "   Claude CLI输出:"
echo "$claude_output" | head -20

echo ""
echo "🏁 诊断完成"
echo ""

# 分析结果
echo "📊 问题分析："
if [ "$with_proxy" = "520" ] && [ "$without_proxy" != "520" ]; then
    echo "   ❌ 问题可能是代理配置导致的520错误"
    echo "   💡 建议：尝试禁用代理或更换代理设置"
elif [ "$with_proxy" = "520" ] && [ "$without_proxy" = "520" ]; then
    echo "   ❌ 问题是API端点本身的520错误"
    echo "   💡 建议：更换API端点或联系API提供商"
elif [ "$with_proxy" = "200" ] || [ "$without_proxy" = "200" ]; then
    echo "   ✅ API调用本身正常，问题可能在Claude CLI配置"
    echo "   💡 建议：检查Claude CLI的环境变量配置"
else
    echo "   ⚠️  需要进一步分析具体错误"
fi
