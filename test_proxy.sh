#!/bin/bash

echo "🌐 代理配置测试脚本"
echo "=================="
echo ""

# 重新加载配置
source ~/.zshrc

echo "📋 当前代理配置："
echo "  https_proxy: $https_proxy"
echo "  http_proxy: $http_proxy"
echo "  HTTPS_PROXY: $HTTPS_PROXY"
echo "  HTTP_PROXY: $HTTP_PROXY"
echo ""

echo "🔍 测试代理连接："
echo ""

# 测试1: 检查代理端口是否开放
echo "1️⃣ 检查代理端口 127.0.0.1:10808："
if nc -z 127.0.0.1 10808 2>/dev/null; then
    echo "   ✅ 代理端口开放"
else
    echo "   ❌ 代理端口未开放或代理未启动"
fi

# 测试2: 通过代理测试HTTP连接
echo ""
echo "2️⃣ 测试HTTP代理连接："
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --proxy http://127.0.0.1:10808 --connect-timeout 5 --max-time 10 http://www.google.com 2>/dev/null)
if [ "$HTTP_STATUS" = "200" ]; then
    echo "   ✅ HTTP代理工作正常"
else
    echo "   ⚠️  HTTP代理测试失败 (状态码: $HTTP_STATUS)"
fi

# 测试3: 通过代理测试HTTPS连接
echo ""
echo "3️⃣ 测试HTTPS代理连接："
HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --proxy http://127.0.0.1:10808 --connect-timeout 5 --max-time 10 https://www.google.com 2>/dev/null)
if [ "$HTTPS_STATUS" = "200" ]; then
    echo "   ✅ HTTPS代理工作正常"
else
    echo "   ⚠️  HTTPS代理测试失败 (状态码: $HTTPS_STATUS)"
fi

# 测试4: 测试Claude API通过代理
echo ""
echo "4️⃣ 测试Claude API（通过代理）："
echo "   发送测试请求到 $ANTHROPIC_BASE_URL..."

API_RESPONSE=$(timeout 15 curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "$ANTHROPIC_BASE_URL/v1/messages" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $ANTHROPIC_API_KEY" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "max_tokens": 10,
    "messages": [{"role": "user", "content": "Hello"}]
  }' 2>/dev/null)

if [ $? -eq 124 ]; then
    echo "   ⏰ API请求超时"
else
    HTTP_STATUS=$(echo "$API_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    echo "   📊 API状态码: $HTTP_STATUS"
    
    case $HTTP_STATUS in
        200)
            echo "   ✅ Claude API通过代理工作正常！"
            ;;
        520)
            echo "   ❌ 520错误 - 服务器错误"
            ;;
        502)
            echo "   ❌ 502错误 - 网关错误"
            ;;
        *)
            echo "   ⚠️  其他状态码: $HTTP_STATUS"
            ;;
    esac
fi

echo ""
echo "🏁 代理测试完成"
echo ""
echo "📝 配置说明："
echo "   代理设置已永久添加到 ~/.zshrc"
echo "   每次打开新终端都会自动加载代理配置"
echo "   无需手动设置 export https_proxy=http://127.0.0.1:10808"
echo ""
echo "🔧 如需临时禁用代理，可运行："
echo "   unset https_proxy http_proxy HTTPS_PROXY HTTP_PROXY"
