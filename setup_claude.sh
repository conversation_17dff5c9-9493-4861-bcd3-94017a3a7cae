#!/bin/bash

# Claude API 配置脚本
# 设置 Anthropic Claude API 的环境变量

echo "正在配置 Claude API 环境变量..."

# 检查.zshrc文件是否存在
ZSHRC_FILE="$HOME/.zshrc"
BASHRC_FILE="$HOME/.bashrc"

# 要添加的环境变量
ANTHROPIC_AUTH_TOKEN="sk-d3eVI4PiSAKMigh2REv5xO45oFXOhTMyqcnVYcxWhBTp49Om"

# 使用备用API端点（根据文档推荐）
ANTHROPIC_BASE_URL="https://pmpjfbhq.cn-nb1.rainapp.top"
echo "使用备用API端点: pmpjfbhq.cn-nb1.rainapp.top"

# 函数：添加环境变量到配置文件
add_to_config_file() {
    local config_file=$1
    local var_name=$2
    local var_value=$3
    
    if [ ! -f "$config_file" ]; then
        echo "创建新的配置文件: $config_file"
        touch "$config_file"
    fi
    
    # 检查变量是否已存在
    if grep -q "export $var_name=" "$config_file"; then
        echo "$var_name 已存在于 $config_file 中，正在更新..."
        # 使用 sed 更新现有的变量
        sed -i.bak "s|export $var_name=.*|export $var_name=$var_value|" "$config_file"
    else
        echo "添加 $var_name 到 $config_file"
        echo "" >> "$config_file"
        echo "# Claude API 配置" >> "$config_file"
        echo "export $var_name=$var_value" >> "$config_file"
    fi
}

# 检测当前使用的 shell
if [ -n "$ZSH_VERSION" ]; then
    echo "检测到 Zsh shell，配置 .zshrc"
    CONFIG_FILE="$ZSHRC_FILE"
elif [ -n "$BASH_VERSION" ]; then
    echo "检测到 Bash shell，配置 .bashrc"
    CONFIG_FILE="$BASHRC_FILE"
else
    echo "未知 shell，默认配置 .bashrc"
    CONFIG_FILE="$BASHRC_FILE"
fi

# 添加环境变量
add_to_config_file "$CONFIG_FILE" "ANTHROPIC_AUTH_TOKEN" "$ANTHROPIC_AUTH_TOKEN"
add_to_config_file "$CONFIG_FILE" "ANTHROPIC_BASE_URL" "$ANTHROPIC_BASE_URL"

echo ""
echo "✅ Claude API 配置完成！"
echo ""
echo "请运行以下命令使配置生效："
if [ "$CONFIG_FILE" = "$ZSHRC_FILE" ]; then
    echo "source ~/.zshrc"
else
    echo "source ~/.bashrc"
fi
echo ""
echo "或者重新打开终端窗口。"
echo ""
echo "配置的环境变量："
echo "  ANTHROPIC_AUTH_TOKEN: $ANTHROPIC_AUTH_TOKEN"
echo "  ANTHROPIC_BASE_URL: $ANTHROPIC_BASE_URL"
echo ""
echo "🔧 故障排除提示："
echo "如果遇到 401 错误，可能的原因："
echo "1. API密钥无效或过期"
echo "2. 账户余额不足"
echo "3. 代理服务认证问题"
echo ""
echo "测试命令："
echo "  claude --print \"Hello, test message\""
echo ""
echo "您现在可以使用 'claude' 命令了！"
