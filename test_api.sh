#!/bin/bash

echo "🧪 Claude API 配置测试"
echo "====================="
echo ""

# 设置环境变量
export ANTHROPIC_API_KEY="sk-d3eVI4PiSAKMigh2REv5xO45oFXOhTMyqcnVYcxWhBTp49Om"
export ANTHROPIC_BASE_URL="https://anyrouter.top"

echo "📋 配置信息："
echo "  API Key: ${ANTHROPIC_API_KEY:0:20}..."
echo "  Base URL: $ANTHROPIC_BASE_URL"
echo ""

echo "🔍 测试步骤："
echo ""

# 测试1: 检查Claude命令
echo "1️⃣ 检查Claude CLI安装："
if command -v claude >/dev/null 2>&1; then
    echo "   ✅ Claude CLI已安装: $(which claude)"
else
    echo "   ❌ Claude CLI未安装"
    exit 1
fi

# 测试2: 测试网络连接
echo ""
echo "2️⃣ 测试网络连接："
if ping -c 1 anyrouter.top >/dev/null 2>&1; then
    echo "   ✅ 可以ping通 anyrouter.top"
else
    echo "   ⚠️  无法ping通 anyrouter.top (可能是防火墙阻止)"
fi

# 测试3: 测试HTTPS连接
echo ""
echo "3️⃣ 测试HTTPS连接："
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -I https://anyrouter.top --connect-timeout 5 --max-time 10)
echo "   📊 HTTPS状态码: $HTTP_STATUS"

# 测试4: 测试API调用
echo ""
echo "4️⃣ 测试API调用："
echo "   发送测试请求..."

API_RESPONSE=$(timeout 15 curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "https://anyrouter.top/v1/messages" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $ANTHROPIC_API_KEY" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "max_tokens": 10,
    "messages": [{"role": "user", "content": "Hello"}]
  }' 2>/dev/null)

if [ $? -eq 124 ]; then
    echo "   ⏰ 请求超时"
else
    HTTP_STATUS=$(echo "$API_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$API_RESPONSE" | grep -v "HTTP_STATUS:")
    
    echo "   📊 API状态码: $HTTP_STATUS"
    
    case $HTTP_STATUS in
        200)
            echo "   ✅ API调用成功！"
            echo "   📝 响应: $(echo "$RESPONSE_BODY" | head -c 100)..."
            ;;
        520)
            echo "   ❌ 520错误 - 服务器错误"
            ;;
        502)
            echo "   ❌ 502错误 - 网关错误"
            ;;
        401)
            echo "   ❌ 401错误 - API密钥无效"
            ;;
        429)
            echo "   ❌ 429错误 - 请求过于频繁"
            ;;
        *)
            echo "   ❌ 其他错误 (状态码: $HTTP_STATUS)"
            if [ -n "$RESPONSE_BODY" ]; then
                echo "   📝 错误信息: $RESPONSE_BODY"
            fi
            ;;
    esac
fi

echo ""
echo "🏁 测试完成"
echo ""

# 如果API调用成功，测试Claude CLI
if [ "$HTTP_STATUS" = "200" ]; then
    echo "5️⃣ 测试Claude CLI："
    echo "   运行Claude命令..."
    echo "Hello from test script" | timeout 10 claude --print 2>/dev/null && echo "   ✅ Claude CLI工作正常" || echo "   ⚠️  Claude CLI可能有问题"
fi

echo ""
echo "📝 配置已设置完成！"
echo "   要使配置永久生效，请运行: source ~/.zshrc"
